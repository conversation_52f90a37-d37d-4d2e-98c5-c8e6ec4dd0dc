# 自动还原备份配置文件
# 请根据您的实际环境修改以下配置

# 基本路径配置
BACKUP_DIR="/path/to/your/backup/directory"  # 修改为您的备份目录路径
LOG_FILE="/var/log/backup_restore.log"       # 日志文件路径
DA_PATH="/usr/local/directadmin"             # DirectAdmin安装路径

# 数据库配置
MYSQL_USER="root"                            # MySQL用户名
MYSQL_PASSWORD=""                            # MySQL密码（留空使用默认认证）
MYSQL_HOST="localhost"                       # MySQL主机

# 邮件服务配置
MAIL_SERVICE="dovecot"                       # 邮件服务名称
MAIL_GROUP="mail"                           # 邮件组名

# 备份选项
BACKUP_CURRENT_STATE="yes"                   # 是否在还原前备份当前状态
RESTORE_DATABASES="yes"                      # 是否还原数据库
RESTORE_WEBSITE_FILES="yes"                  # 是否还原网站文件
RESTORE_EMAIL_DATA="yes"                     # 是否还原邮件数据
RESTORE_CRONTAB="yes"                       # 是否还原cron任务

# 服务重启配置
RESTART_APACHE="yes"                        # 是否重启Apache
RESTART_MAIL_SERVICES="yes"                 # 是否重启邮件服务
RESTART_FTP="yes"                          # 是否重启FTP服务

# 安全选项
REQUIRE_ROOT="yes"                          # 是否要求root权限
USE_LOCK_FILE="yes"                        # 是否使用锁文件防止重复运行

# 通知配置
SEND_EMAIL_NOTIFICATION="no"                # 是否发送邮件通知
NOTIFICATION_EMAIL=""                       # 通知邮箱地址
SMTP_SERVER=""                             # SMTP服务器

# 高级选项
COMPRESSION_TYPE="zstd"                     # 压缩类型 (gzip, zstd, xz)
TEMP_DIR="/tmp"                            # 临时目录
MAX_LOG_SIZE="100M"                        # 最大日志文件大小
LOG_ROTATION="yes"                         # 是否启用日志轮转
