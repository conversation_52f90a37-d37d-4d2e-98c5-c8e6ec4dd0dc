# DirectAdmin 备份还原脚本使用指南

## 脚本概览

| 脚本名称 | 用途 | 推荐场景 |
|---------|------|----------|
| `run_restore.sh` | 一键还原 | **首次使用推荐** |
| `restore_website.sh` | 网站还原 | 日常还原操作 |
| `check_backup.sh` | 备份检查 | 验证备份完整性 |
| `auto_restore_cron.sh` | 定时还原 | 自动化部署 |
| `cron.sh` | 完整还原 | 复杂环境 |

## 快速开始（3步完成）

### 第1步：检查备份
```bash
chmod +x check_backup.sh
./check_backup.sh
```

### 第2步：一键还原
```bash
chmod +x run_restore.sh
./run_restore.sh
```

### 第3步：验证结果
检查以下目录：
- 网站文件：`/home/<USER>/domains/域名/public_html`
- 邮件数据：`/home/<USER>/imap/域名`

## 详细使用说明

### 1. 备份文件检查

运行检查脚本验证备份完整性：
```bash
./check_backup.sh
```

**输出示例：**
```
✓ 用户配置文件 (user.conf) - 大小: 2.1K
✓ 网站文件压缩包 (home.tar.zst) - 大小: 15M
✓ Cron任务配置 (crontab.conf) - 大小: 156B
```

### 2. 手动还原

#### 方法一：使用一键脚本（推荐）
```bash
./run_restore.sh
```

#### 方法二：直接运行还原脚本
```bash
./restore_website.sh
```

### 3. 设置定时还原

#### 编辑crontab
```bash
crontab -e
```

#### 添加定时任务
```bash
# 每天凌晨2点检查并还原
0 2 * * * /home/<USER>/auto_restore_cron.sh

# 每小时检查一次
0 * * * * /home/<USER>/auto_restore_cron.sh

# 每分钟检查（测试用）
* * * * * /home/<USER>/auto_restore_cron.sh
```

## 故障排除

### 常见问题

#### 1. 权限错误
```bash
# 解决方案：设置正确权限
chmod +x *.sh
```

#### 2. zstd 命令未找到
```bash
# CentOS/RHEL
sudo yum install zstd
# 或
sudo dnf install zstd

# Ubuntu/Debian
sudo apt-get install zstd
```

#### 3. 日志文件权限问题
脚本会自动使用本地日志文件，避免权限问题。

#### 4. 备份文件损坏
```bash
# 检查压缩文件完整性
zstd -t backup/home.tar.zst
```

### 查看日志

#### 实时查看日志
```bash
tail -f restore_*.log
```

#### 查看错误信息
```bash
grep -i error restore_*.log
grep -i 失败 restore_*.log
```

## 高级配置

### 自定义备份路径

编辑脚本中的路径变量：
```bash
BACKUP_DIR="/path/to/your/backup"
```

### 邮件通知

在 `auto_restore_cron.sh` 中启用邮件通知：
```bash
# 取消注释以下行
echo "还原成功 - $(date)" | mail -s "还原成功" <EMAIL>
```

### 日志轮转

脚本自动清理7天前的日志文件。修改保留天数：
```bash
# 保留30天的日志
find "$SCRIPT_DIR" -name "cron_restore_*.log" -mtime +30 -delete
```

## 安全注意事项

1. **备份当前数据**：还原前请备份现有重要数据
2. **测试环境验证**：建议先在测试环境中验证脚本
3. **权限控制**：确保脚本文件权限设置正确
4. **日志监控**：定期检查日志文件

## 支持的备份类型

- ✅ 用户配置 (user.conf)
- ✅ 网站文件 (home.tar.zst)
- ✅ 数据库 (*.db, *.sql)
- ✅ 邮件数据 (email_data/)
- ✅ 域名配置 (domain.conf)
- ✅ FTP配置 (ftp.conf)
- ✅ Cron任务 (crontab.conf)
- ✅ 子域名列表 (subdomain.list)

## 脚本特性

- 🔒 **锁文件机制**：防止重复运行
- 📝 **详细日志**：记录所有操作步骤
- 🎨 **彩色输出**：易于识别状态信息
- 🔄 **自动备份**：还原前备份现有数据
- ⚡ **智能检测**：自动选择最佳还原方法
- 🛡️ **错误处理**：完善的错误检查和处理

## 目录结构

还原后的目录结构：
```
/home/<USER>/
├── domains/
│   └── 域名/
│       ├── public_html/     # 网站文件
│       └── private_html/
├── imap/
│   └── 域名/               # 邮件数据
└── .ssh/                   # SSH配置
```

## 联系支持

如遇到问题：
1. 查看日志文件获取详细错误信息
2. 运行 `check_backup.sh` 验证备份文件
3. 确保所有依赖工具已安装
4. 检查文件权限设置

---

**版本**: 1.0  
**兼容性**: DirectAdmin 1.60+  
**测试环境**: CentOS 7/8, Ubuntu 18.04/20.04
