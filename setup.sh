#!/bin/bash

# 快速设置脚本
# 用于配置自动还原环境

echo "=========================================="
echo "DirectAdmin 备份还原脚本 - 快速设置"
echo "=========================================="

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "错误: 请以root用户运行此脚本"
    exit 1
fi

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
echo "脚本目录: $SCRIPT_DIR"

# 检查必要的工具
echo "检查必要工具..."

check_command() {
    if command -v "$1" >/dev/null 2>&1; then
        echo "✓ $1 已安装"
        return 0
    else
        echo "✗ $1 未安装"
        return 1
    fi
}

# 检查工具列表
tools_missing=0
for tool in zstd tar mysql; do
    if ! check_command "$tool"; then
        tools_missing=1
    fi
done

# 安装缺失的工具
if [ $tools_missing -eq 1 ]; then
    echo ""
    echo "正在安装缺失的工具..."
    
    # 检测系统类型
    if [ -f /etc/redhat-release ]; then
        # CentOS/RHEL
        yum install -y zstd tar mysql || dnf install -y zstd tar mysql
    elif [ -f /etc/debian_version ]; then
        # Ubuntu/Debian
        apt-get update
        apt-get install -y zstd tar mysql-client
    else
        echo "警告: 无法自动安装工具，请手动安装: zstd, tar, mysql"
    fi
fi

# 设置脚本权限
echo ""
echo "设置脚本权限..."
chmod +x "$SCRIPT_DIR/cron.sh"
chmod +x "$SCRIPT_DIR/simple_restore.sh"
chmod 600 "$SCRIPT_DIR/restore_config.conf"

echo "✓ 权限设置完成"

# 检查备份目录
echo ""
echo "检查备份目录..."
if [ -d "$SCRIPT_DIR/backup" ]; then
    echo "✓ 备份目录存在: $SCRIPT_DIR/backup"
    
    # 列出备份文件
    echo ""
    echo "备份文件列表:"
    ls -la "$SCRIPT_DIR/backup/"
    
    # 检查关键文件
    if [ -f "$SCRIPT_DIR/backup/user.conf" ]; then
        echo ""
        echo "用户配置信息:"
        grep -E "^(username|domain|email)=" "$SCRIPT_DIR/backup/user.conf"
    fi
else
    echo "✗ 备份目录不存在: $SCRIPT_DIR/backup"
    echo "请确保备份文件位于正确的目录中"
fi

# 创建日志目录
echo ""
echo "创建日志目录..."
mkdir -p /var/log
touch /var/log/backup_restore.log
chmod 644 /var/log/backup_restore.log
echo "✓ 日志文件已创建: /var/log/backup_restore.log"

# 提供使用建议
echo ""
echo "=========================================="
echo "设置完成！"
echo "=========================================="
echo ""
echo "使用方法:"
echo "1. 手动运行还原:"
echo "   sudo $SCRIPT_DIR/simple_restore.sh"
echo ""
echo "2. 设置定时任务:"
echo "   crontab -e"
echo "   添加: 0 2 * * * $SCRIPT_DIR/simple_restore.sh"
echo ""
echo "3. 查看日志:"
echo "   tail -f /var/log/backup_restore.log"
echo "   tail -f $SCRIPT_DIR/restore.log"
echo ""
echo "注意事项:"
echo "- 还原前请确保已备份当前数据"
echo "- 建议先在测试环境中验证"
echo "- 大文件还原可能需要较长时间"
echo ""
echo "=========================================="
