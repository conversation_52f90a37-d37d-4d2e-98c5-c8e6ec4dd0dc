#!/bin/bash

# 自动还原定时任务脚本
# 专门用于cron定时执行

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_DIR="$SCRIPT_DIR/backup"
LOG_FILE="$SCRIPT_DIR/cron_restore_$(date +%Y%m%d).log"
LOCK_FILE="/tmp/auto_restore.lock"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" >> "$LOG_FILE"
}

# 检查锁文件
if [ -f "$LOCK_FILE" ]; then
    pid=$(cat "$LOCK_FILE")
    if ps -p "$pid" > /dev/null 2>&1; then
        log "还原进程已在运行 (PID: $pid)，退出"
        exit 0
    else
        rm -f "$LOCK_FILE"
    fi
fi

# 创建锁文件
echo $$ > "$LOCK_FILE"

# 清理函数
cleanup() {
    rm -f "$LOCK_FILE"
}
trap cleanup EXIT

log "========== 开始自动还原检查 =========="

# 检查备份目录是否存在
if [ ! -d "$BACKUP_DIR" ]; then
    log "错误: 备份目录不存在 - $BACKUP_DIR"
    exit 1
fi

# 检查是否有新的备份文件
if [ ! -f "$BACKUP_DIR/user.conf" ]; then
    log "警告: 未找到用户配置文件，跳过还原"
    exit 0
fi

# 检查上次还原时间
LAST_RESTORE_FILE="$SCRIPT_DIR/.last_restore"
if [ -f "$LAST_RESTORE_FILE" ]; then
    last_restore=$(cat "$LAST_RESTORE_FILE")
    backup_time=$(stat -c %Y "$BACKUP_DIR/user.conf" 2>/dev/null || echo 0)
    
    if [ "$backup_time" -le "$last_restore" ]; then
        log "备份文件未更新，跳过还原"
        exit 0
    fi
fi

log "发现新的备份文件，开始还原..."

# 执行还原
if [ -f "$SCRIPT_DIR/restore_website.sh" ]; then
    log "使用 restore_website.sh 进行还原"
    "$SCRIPT_DIR/restore_website.sh" >> "$LOG_FILE" 2>&1
    restore_result=$?
else
    log "错误: 还原脚本不存在"
    exit 1
fi

# 检查还原结果
if [ $restore_result -eq 0 ]; then
    log "还原成功完成"
    echo $(date +%s) > "$LAST_RESTORE_FILE"
    
    # 可选：发送成功通知
    # echo "网站还原成功 - $(date)" | mail -s "还原成功" <EMAIL>
else
    log "还原失败，退出码: $restore_result"
    
    # 可选：发送失败通知
    # echo "网站还原失败 - $(date)" | mail -s "还原失败" <EMAIL>
fi

log "========== 自动还原检查结束 =========="

# 清理旧日志文件（保留最近7天）
find "$SCRIPT_DIR" -name "cron_restore_*.log" -mtime +7 -delete 2>/dev/null

cleanup
