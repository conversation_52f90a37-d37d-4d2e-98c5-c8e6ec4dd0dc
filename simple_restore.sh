#!/bin/bash

# 简化版自动还原脚本
# 专门针对当前备份文件结构设计

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_DIR="$SCRIPT_DIR/backup"
LOG_FILE="$SCRIPT_DIR/restore.log"
LOCK_FILE="/tmp/simple_restore.lock"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_message() {
    local message="[$(date '+%Y-%m-%d %H:%M:%S')] $1"
    echo -e "$message" | tee -a "$LOG_FILE"
}

log_success() {
    log_message "${GREEN}✓ $1${NC}"
}

log_warning() {
    log_message "${YELLOW}⚠ $1${NC}"
}

log_error() {
    log_message "${RED}✗ $1${NC}"
}

# 检查锁文件
check_lock() {
    if [ -f "$LOCK_FILE" ]; then
        local pid=$(cat "$LOCK_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            log_error "还原进程已在运行 (PID: $pid)"
            exit 1
        else
            rm -f "$LOCK_FILE"
        fi
    fi
    echo $$ > "$LOCK_FILE"
}

# 清理函数
cleanup() {
    if [ -f "$LOCK_FILE" ]; then
        rm -f "$LOCK_FILE"
    fi
}

# 检查备份文件
check_backup_files() {
    log_message "检查备份文件..."
    
    if [ ! -d "$BACKUP_DIR" ]; then
        log_error "备份目录不存在: $BACKUP_DIR"
        exit 1
    fi
    
    # 检查关键文件
    local required_files=("user.conf")
    for file in "${required_files[@]}"; do
        if [ ! -f "$BACKUP_DIR/$file" ]; then
            log_error "缺少必要文件: $file"
            exit 1
        fi
    done
    
    log_success "备份文件检查完成"
}

# 读取用户信息
read_user_info() {
    if [ -f "$BACKUP_DIR/user.conf" ]; then
        USERNAME=$(grep "^username=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
        DOMAIN=$(grep "^domain=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
        EMAIL=$(grep "^email=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
        
        log_message "用户信息:"
        log_message "  用户名: $USERNAME"
        log_message "  域名: $DOMAIN"
        log_message "  邮箱: $EMAIL"
    else
        log_error "无法读取用户配置文件"
        exit 1
    fi
}

# 创建用户目录结构
create_user_directories() {
    log_message "创建用户目录结构..."
    
    local user_home="/home/<USER>"
    local domain_dir="$user_home/domains/$DOMAIN"
    local public_html="$domain_dir/public_html"
    local private_html="$domain_dir/private_html"
    local imap_dir="$user_home/imap/$DOMAIN"
    
    # 创建目录
    mkdir -p "$user_home"
    mkdir -p "$domain_dir"
    mkdir -p "$public_html"
    mkdir -p "$private_html"
    mkdir -p "$imap_dir"
    mkdir -p "$user_home/.ssh"
    
    # 设置权限
    if id "$USERNAME" &>/dev/null; then
        chown -R "$USERNAME:$USERNAME" "$user_home"
    else
        log_warning "用户 $USERNAME 不存在，请先创建用户"
    fi
    
    log_success "用户目录结构创建完成"
}

# 还原网站文件
restore_website_files() {
    log_message "还原网站文件..."
    
    if [ -f "$BACKUP_DIR/home.tar.zst" ]; then
        local temp_dir="/tmp/restore_$$"
        mkdir -p "$temp_dir"
        
        cd "$temp_dir"
        
        # 解压文件
        if command -v zstd >/dev/null 2>&1; then
            zstd -d -c "$BACKUP_DIR/home.tar.zst" | tar -xf -
        else
            log_error "zstd 命令未找到，请安装 zstd"
            return 1
        fi
        
        if [ $? -eq 0 ] && [ -d "$temp_dir/home/<USER>" ]; then
            # 备份现有文件
            if [ -d "/home/<USER>" ]; then
                mv "/home/<USER>" "/home/<USER>"
            fi
            
            # 移动文件
            mv "$temp_dir/home/<USER>" "/home/"
            
            # 设置权限
            if id "$USERNAME" &>/dev/null; then
                chown -R "$USERNAME:$USERNAME" "/home/<USER>"
            fi
            
            log_success "网站文件还原完成"
        else
            log_error "解压网站文件失败"
        fi
        
        # 清理临时目录
        rm -rf "$temp_dir"
    else
        log_warning "未找到网站文件备份 (home.tar.zst)"
    fi
}

# 还原域名配置
restore_domain_config() {
    log_message "还原域名配置..."
    
    local domain_backup_dir="$BACKUP_DIR/$DOMAIN"
    if [ -d "$domain_backup_dir" ]; then
        # 复制域名配置文件
        local target_dir="/usr/local/directadmin/data/users/$USERNAME/domains/$DOMAIN"
        mkdir -p "$target_dir"
        
        if [ -f "$domain_backup_dir/domain.conf" ]; then
            cp "$domain_backup_dir/domain.conf" "$target_dir/"
            log_success "域名配置文件已还原"
        fi
        
        if [ -f "$domain_backup_dir/domain.ip_list" ]; then
            cp "$domain_backup_dir/domain.ip_list" "$target_dir/"
        fi
        
        if [ -f "$domain_backup_dir/subdomain.list" ]; then
            cp "$domain_backup_dir/subdomain.list" "$target_dir/"
        fi
        
    else
        log_warning "未找到域名配置目录: $domain_backup_dir"
    fi
}

# 还原数据库
restore_database() {
    log_message "还原数据库..."
    
    local db_file="$BACKUP_DIR/$DOMAIN/${DOMAIN}.db"
    if [ -f "$db_file" ]; then
        # 假设数据库名为用户名_域名（去掉点）
        local db_name="${USERNAME}_$(echo $DOMAIN | tr '.' '_')"
        
        log_message "还原数据库: $db_name"
        
        # 创建数据库
        mysql -e "CREATE DATABASE IF NOT EXISTS \`$db_name\`;" 2>/dev/null
        
        # 还原数据
        if mysql "$db_name" < "$db_file" 2>/dev/null; then
            log_success "数据库还原完成: $db_name"
        else
            log_warning "数据库还原失败，可能需要手动处理"
        fi
    else
        log_warning "未找到数据库备份文件"
    fi
}

# 还原邮件数据
restore_email() {
    log_message "还原邮件数据..."
    
    local email_backup_dir="$BACKUP_DIR/email_data"
    local target_email_dir="/home/<USER>/imap/$DOMAIN"
    
    if [ -d "$email_backup_dir" ]; then
        mkdir -p "$target_email_dir"
        cp -r "$email_backup_dir"/* "$target_email_dir/" 2>/dev/null
        
        # 设置权限
        if id "$USERNAME" &>/dev/null; then
            chown -R "$USERNAME:mail" "$target_email_dir"
            chmod -R 660 "$target_email_dir"
        fi
        
        log_success "邮件数据还原完成"
    else
        log_warning "未找到邮件数据备份"
    fi
}

# 还原cron任务
restore_crontab() {
    log_message "还原cron任务..."
    
    if [ -f "$BACKUP_DIR/crontab.conf" ]; then
        if id "$USERNAME" &>/dev/null; then
            # 为用户设置crontab
            sudo -u "$USERNAME" crontab "$BACKUP_DIR/crontab.conf" 2>/dev/null
            if [ $? -eq 0 ]; then
                log_success "Cron任务还原完成"
            else
                log_warning "Cron任务还原失败"
            fi
        else
            log_warning "用户不存在，无法还原cron任务"
        fi
    else
        log_warning "未找到cron配置文件"
    fi
}

# 显示还原摘要
show_summary() {
    log_message "========== 还原摘要 =========="
    log_message "用户名: $USERNAME"
    log_message "域名: $DOMAIN"
    log_message "邮箱: $EMAIL"
    log_message "备份目录: $BACKUP_DIR"
    log_message "日志文件: $LOG_FILE"
    log_message "================================"
}

# 主函数
main() {
    log_message "========== 开始简化还原流程 =========="
    
    # 设置陷阱
    trap cleanup EXIT
    
    # 检查锁文件
    check_lock
    
    # 检查备份文件
    check_backup_files
    
    # 读取用户信息
    read_user_info
    
    # 执行还原步骤
    create_user_directories
    restore_website_files
    restore_domain_config
    restore_database
    restore_email
    restore_crontab
    
    # 显示摘要
    show_summary
    
    log_success "========== 还原流程完成 =========="
    
    # 清理
    cleanup
}

# 检查是否为root用户
if [ "$EUID" -ne 0 ]; then
    echo "请以root用户运行此脚本"
    exit 1
fi

# 运行主函数
main "$@"
