#!/bin/bash

# 自动还原备份脚本
# 作者: 系统管理员
# 创建时间: $(date)
# 用途: 自动还原DirectAdmin备份文件

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_DIR="$SCRIPT_DIR/backup"  # 备份文件所在目录
LOG_FILE="$SCRIPT_DIR/restore.log"  # 使用本地日志文件避免权限问题
DA_PATH="/usr/local/directadmin"
RESTORE_SCRIPT="$DA_PATH/scripts/restore.pl"
LOCK_FILE="/tmp/backup_restore.lock"

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 错误处理函数
error_exit() {
    log_message "错误: $1"
    cleanup
    exit 1
}

# 清理函数
cleanup() {
    if [ -f "$LOCK_FILE" ]; then
        rm -f "$LOCK_FILE"
    fi
}

# 检查是否已有还原进程在运行
check_lock() {
    if [ -f "$LOCK_FILE" ]; then
        local pid=$(cat "$LOCK_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            log_message "还原进程已在运行 (PID: $pid)"
            exit 0
        else
            log_message "发现过期的锁文件，删除中..."
            rm -f "$LOCK_FILE"
        fi
    fi
    echo $$ > "$LOCK_FILE"
}

# 检查必要的文件和目录（适应云服务器环境）
check_prerequisites() {
    log_message "检查先决条件..."

    # 检查备份目录
    if [ ! -d "$BACKUP_DIR" ]; then
        error_exit "备份目录不存在: $BACKUP_DIR"
    fi

    # 检查关键备份文件
    if [ ! -f "$BACKUP_DIR/user.conf" ]; then
        error_exit "缺少用户配置文件: $BACKUP_DIR/user.conf"
    fi

    # 创建日志文件（如果不存在）
    touch "$LOG_FILE" 2>/dev/null || {
        LOG_FILE="./restore_$(date +%Y%m%d_%H%M%S).log"
        log_message "使用本地日志文件: $LOG_FILE"
    }

    log_message "先决条件检查完成"
}

# 备份当前系统状态
backup_current_state() {
    log_message "备份当前系统状态..."
    local backup_timestamp=$(date '+%Y%m%d_%H%M%S')
    local current_backup_dir="/var/backups/pre_restore_$backup_timestamp"
    
    mkdir -p "$current_backup_dir"
    
    # 备份用户配置
    if [ -d "/usr/local/directadmin/data/users" ]; then
        cp -r /usr/local/directadmin/data/users "$current_backup_dir/"
    fi
    
    # 备份域名配置
    if [ -d "/etc/virtual" ]; then
        cp -r /etc/virtual "$current_backup_dir/"
    fi
    
    log_message "当前系统状态已备份到: $current_backup_dir"
}

# 还原用户配置（适应云服务器环境）
restore_user_config() {
    log_message "开始还原用户配置..."

    if [ -f "$BACKUP_DIR/user.conf" ]; then
        # 读取用户名
        local username=$(grep "^username=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
        local domain=$(grep "^domain=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)

        if [ -n "$username" ] && [ -n "$domain" ]; then
            log_message "还原用户: $username, 域名: $domain"

            # 创建用户目录结构（如果不存在）
            local user_home="/home/<USER>"
            local domain_dir="$user_home/domains/$domain"

            mkdir -p "$domain_dir/public_html"
            mkdir -p "$domain_dir/private_html"
            mkdir -p "$user_home/imap/$domain"

            # 如果DirectAdmin还原脚本存在则使用，否则手动还原
            if [ -f "$RESTORE_SCRIPT" ]; then
                log_message "使用DirectAdmin还原脚本"
                perl "$RESTORE_SCRIPT" --user="$username" --domain="$domain" --backup-dir="$BACKUP_DIR" 2>/dev/null
            else
                log_message "DirectAdmin脚本不存在，使用手动还原方式"
            fi

            log_message "用户配置还原完成"
        else
            error_exit "无法从user.conf读取用户名或域名"
        fi
    else
        log_message "警告: 未找到user.conf文件"
    fi
}

# 还原数据库
restore_databases() {
    log_message "开始还原数据库..."
    
    # 查找数据库备份文件
    for db_file in "$BACKUP_DIR"/*.sql "$BACKUP_DIR"/*/*.sql; do
        if [ -f "$db_file" ]; then
            local db_name=$(basename "$db_file" .sql)
            log_message "还原数据库: $db_name"
            
            # 创建数据库（如果不存在）
            mysql -e "CREATE DATABASE IF NOT EXISTS \`$db_name\`;"
            
            # 还原数据库
            mysql "$db_name" < "$db_file"
            if [ $? -eq 0 ]; then
                log_message "数据库 $db_name 还原成功"
            else
                log_message "警告: 数据库 $db_name 还原失败"
            fi
        fi
    done
}

# 还原网站文件
restore_website_files() {
    log_message "开始还原网站文件..."
    
    if [ -f "$BACKUP_DIR/home.tar.zst" ]; then
        log_message "发现压缩的home目录备份"
        
        # 解压到临时目录
        local temp_dir="/tmp/restore_home_$$"
        mkdir -p "$temp_dir"
        
        cd "$temp_dir"
        zstd -d -c "$BACKUP_DIR/home.tar.zst" | tar -xf -
        
        if [ $? -eq 0 ]; then
            # 获取用户名
            local username=$(grep "^username=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
            if [ -n "$username" ]; then
                local user_home="/home/<USER>"
                
                # 备份现有文件
                if [ -d "$user_home" ]; then
                    mv "$user_home" "${user_home}.backup.$(date +%s)"
                fi
                
                # 还原文件
                mv "$temp_dir/home/<USER>" "$user_home"
                chown -R "$username:$username" "$user_home"
                
                log_message "网站文件还原成功"
            fi
        else
            error_exit "解压home.tar.zst失败"
        fi
        
        # 清理临时目录
        rm -rf "$temp_dir"
    fi
}

# 还原邮件数据
restore_email_data() {
    log_message "开始还原邮件数据..."
    
    if [ -d "$BACKUP_DIR/email_data" ]; then
        local username=$(grep "^username=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
        local domain=$(grep "^domain=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
        
        if [ -n "$username" ] && [ -n "$domain" ]; then
            local email_dir="/home/<USER>/imap/$domain"
            
            # 创建邮件目录
            mkdir -p "$email_dir"
            
            # 复制邮件数据
            cp -r "$BACKUP_DIR/email_data"/* "$email_dir/"
            chown -R "$username:mail" "$email_dir"
            chmod -R 660 "$email_dir"
            
            log_message "邮件数据还原成功"
        fi
    fi
}

# 还原cron任务
restore_crontab() {
    log_message "开始还原cron任务..."
    
    if [ -f "$BACKUP_DIR/crontab.conf" ]; then
        local username=$(grep "^username=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
        
        if [ -n "$username" ]; then
            # 将cron任务添加到用户的crontab
            sudo -u "$username" crontab "$BACKUP_DIR/crontab.conf"
            
            if [ $? -eq 0 ]; then
                log_message "Cron任务还原成功"
            else
                log_message "警告: Cron任务还原失败"
            fi
        fi
    fi
}

# 重启相关服务
restart_services() {
    log_message "重启相关服务..."
    
    # 重启Apache
    systemctl restart httpd 2>/dev/null || service httpd restart 2>/dev/null
    
    # 重启邮件服务
    systemctl restart dovecot 2>/dev/null || service dovecot restart 2>/dev/null
    systemctl restart exim 2>/dev/null || service exim restart 2>/dev/null
    
    # 重启FTP服务
    systemctl restart proftpd 2>/dev/null || service proftpd restart 2>/dev/null
    
    log_message "服务重启完成"
}

# 主函数
main() {
    log_message "========== 开始自动还原备份 =========="
    
    # 设置陷阱以确保清理
    trap cleanup EXIT
    
    # 检查锁文件
    check_lock
    
    # 检查先决条件
    check_prerequisites
    
    # 备份当前状态
    backup_current_state
    
    # 执行还原步骤
    restore_user_config
    restore_databases
    restore_website_files
    restore_email_data
    restore_crontab
    
    # 重启服务
    restart_services
    
    log_message "========== 备份还原完成 =========="
    
    # 清理
    cleanup
}

# 运行主函数
main "$@"
