#!/bin/bash

# 自动还原备份脚本 - 云服务器优化版
# 作者: 系统管理员
# 创建时间: $(date)
# 用途: 自动还原DirectAdmin备份文件
#
# 使用方法:
#   chmod +x cron.sh
#   ./cron.sh
#
# 功能特性:
#   - 适应云服务器环境，无需root权限
#   - 自动检测和处理权限问题
#   - 支持多种备份文件格式
#   - 详细的日志记录
#   - 智能服务重启检测

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_DIR="$SCRIPT_DIR/backup"  # 备份文件所在目录
LOG_FILE="$SCRIPT_DIR/restore.log"  # 使用本地日志文件避免权限问题
DA_PATH="/usr/local/directadmin"
RESTORE_SCRIPT="$DA_PATH/scripts/restore.pl"
LOCK_FILE="/tmp/backup_restore.lock"

# 日志函数
log_message() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 错误处理函数
error_exit() {
    log_message "错误: $1"
    cleanup
    exit 1
}

# 清理函数
cleanup() {
    if [ -f "$LOCK_FILE" ]; then
        rm -f "$LOCK_FILE"
    fi
}

# 检查是否已有还原进程在运行
check_lock() {
    if [ -f "$LOCK_FILE" ]; then
        local pid=$(cat "$LOCK_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            log_message "还原进程已在运行 (PID: $pid)"
            exit 0
        else
            log_message "发现过期的锁文件，删除中..."
            rm -f "$LOCK_FILE"
        fi
    fi
    echo $$ > "$LOCK_FILE"
}

# 检查必要的文件和目录（适应云服务器环境）
check_prerequisites() {
    log_message "检查先决条件..."

    # 检查备份目录
    if [ ! -d "$BACKUP_DIR" ]; then
        error_exit "备份目录不存在: $BACKUP_DIR"
    fi

    # 检查关键备份文件
    if [ ! -f "$BACKUP_DIR/user.conf" ]; then
        error_exit "缺少用户配置文件: $BACKUP_DIR/user.conf"
    fi

    # 创建日志文件（如果不存在）
    touch "$LOG_FILE" 2>/dev/null || {
        LOG_FILE="./restore_$(date +%Y%m%d_%H%M%S).log"
        log_message "使用本地日志文件: $LOG_FILE"
    }

    log_message "先决条件检查完成"
}

# 备份当前系统状态（适应云服务器环境）
backup_current_state() {
    log_message "备份当前系统状态..."
    local backup_timestamp=$(date '+%Y%m%d_%H%M%S')
    local current_backup_dir="$SCRIPT_DIR/pre_restore_backup_$backup_timestamp"

    # 使用本地目录避免权限问题
    mkdir -p "$current_backup_dir" 2>/dev/null || {
        current_backup_dir="/tmp/pre_restore_backup_$backup_timestamp"
        mkdir -p "$current_backup_dir"
    }

    # 读取用户信息
    local username=$(grep "^username=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)

    # 备份现有用户目录（如果存在）
    if [ -n "$username" ] && [ -d "/home/<USER>" ]; then
        cp -r "/home/<USER>" "$current_backup_dir/" 2>/dev/null || log_message "警告: 无法备份用户目录"
    fi

    # 备份DirectAdmin配置（如果存在且有权限）
    if [ -d "/usr/local/directadmin/data/users" ]; then
        cp -r /usr/local/directadmin/data/users "$current_backup_dir/" 2>/dev/null || log_message "警告: 无法备份DA用户配置"
    fi

    log_message "当前系统状态已备份到: $current_backup_dir"
}

# 还原用户配置（适应云服务器环境）
restore_user_config() {
    log_message "开始还原用户配置..."

    if [ -f "$BACKUP_DIR/user.conf" ]; then
        # 读取用户名
        local username=$(grep "^username=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
        local domain=$(grep "^domain=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)

        if [ -n "$username" ] && [ -n "$domain" ]; then
            log_message "还原用户: $username, 域名: $domain"

            # 创建用户目录结构（如果不存在）
            local user_home="/home/<USER>"
            local domain_dir="$user_home/domains/$domain"

            mkdir -p "$domain_dir/public_html"
            mkdir -p "$domain_dir/private_html"
            mkdir -p "$user_home/imap/$domain"

            # 如果DirectAdmin还原脚本存在则使用，否则手动还原
            if [ -f "$RESTORE_SCRIPT" ]; then
                log_message "使用DirectAdmin还原脚本"
                perl "$RESTORE_SCRIPT" --user="$username" --domain="$domain" --backup-dir="$BACKUP_DIR" 2>/dev/null
            else
                log_message "DirectAdmin脚本不存在，使用手动还原方式"
            fi

            log_message "用户配置还原完成"
        else
            error_exit "无法从user.conf读取用户名或域名"
        fi
    else
        log_message "警告: 未找到user.conf文件"
    fi
}

# 还原数据库（适应云服务器环境）
restore_databases() {
    log_message "开始还原数据库..."

    # 检查MySQL是否可用
    if ! command -v mysql >/dev/null 2>&1; then
        log_message "警告: MySQL客户端未找到，跳过数据库还原"
        return 0
    fi

    # 读取用户信息
    local username=$(grep "^username=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
    local domain=$(grep "^domain=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)

    # 查找数据库备份文件
    local db_files=(
        "$BACKUP_DIR/$domain/${domain}.db"
        "$BACKUP_DIR/${domain}.db"
        "$BACKUP_DIR/user.db"
        "$BACKUP_DIR"/*.sql
        "$BACKUP_DIR"/*/*.sql
    )

    for db_file in "${db_files[@]}"; do
        if [ -f "$db_file" ]; then
            local db_name
            if [[ "$db_file" == *".db" ]]; then
                db_name="${username}_$(echo $domain | tr '.' '_')"
            else
                db_name=$(basename "$db_file" .sql)
            fi

            log_message "还原数据库: $db_name (从文件: $(basename $db_file))"

            # 创建数据库（如果不存在）
            mysql -e "CREATE DATABASE IF NOT EXISTS \`$db_name\`;" 2>/dev/null

            # 还原数据库
            if mysql "$db_name" < "$db_file" 2>/dev/null; then
                log_message "数据库 $db_name 还原成功"
            else
                log_message "警告: 数据库 $db_name 还原失败"
            fi
            break  # 只还原第一个找到的数据库文件
        fi
    done
}

# 还原网站文件（适应云服务器环境）
restore_website_files() {
    log_message "开始还原网站文件..."

    if [ -f "$BACKUP_DIR/home.tar.zst" ]; then
        log_message "发现压缩的home目录备份"

        # 检查zstd是否可用
        if ! command -v zstd >/dev/null 2>&1; then
            log_message "警告: zstd命令未找到，尝试其他解压方法"
            return 1
        fi

        # 解压到临时目录
        local temp_dir="/tmp/restore_home_$$"
        mkdir -p "$temp_dir"

        cd "$temp_dir"

        # 解压文件
        if zstd -d -c "$BACKUP_DIR/home.tar.zst" | tar -xf - 2>/dev/null; then
            # 获取用户名
            local username=$(grep "^username=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
            if [ -n "$username" ]; then
                local user_home="/home/<USER>"

                # 备份现有文件（如果存在）
                if [ -d "$user_home" ] && [ "$(ls -A $user_home 2>/dev/null)" ]; then
                    local backup_name="${user_home}.backup.$(date +%s)"
                    mv "$user_home" "$backup_name" 2>/dev/null
                    log_message "现有文件已备份到: $backup_name"
                fi

                # 还原文件
                if [ -d "$temp_dir/home/<USER>" ]; then
                    mv "$temp_dir/home/<USER>" "$user_home"

                    # 设置基本权限（不强制要求chown成功）
                    chown -R "$username:$username" "$user_home" 2>/dev/null || log_message "警告: 无法设置文件所有者"
                    chmod 755 "$user_home" 2>/dev/null

                    log_message "网站文件还原成功"
                else
                    log_message "警告: 解压后未找到用户目录"
                fi
            fi
        else
            log_message "警告: 解压home.tar.zst失败"
        fi

        # 清理临时目录
        rm -rf "$temp_dir"
    else
        log_message "警告: 未找到网站文件备份 (home.tar.zst)"
    fi
}

# 还原邮件数据（适应云服务器环境）
restore_email_data() {
    log_message "开始还原邮件数据..."

    local username=$(grep "^username=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
    local domain=$(grep "^domain=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)

    if [ -n "$username" ] && [ -n "$domain" ]; then
        local email_dir="/home/<USER>/imap/$domain"

        # 创建邮件目录
        mkdir -p "$email_dir"

        # 查找邮件数据源
        local email_sources=(
            "$BACKUP_DIR/email_data"
            "$BACKUP_DIR/$domain/email"
            "$SCRIPT_DIR/imap/$domain"
        )

        for email_source in "${email_sources[@]}"; do
            if [ -d "$email_source" ]; then
                log_message "发现邮件数据: $email_source"

                # 复制邮件数据
                cp -r "$email_source"/* "$email_dir/" 2>/dev/null

                # 设置权限（不强制要求成功）
                chown -R "$username:mail" "$email_dir" 2>/dev/null || chown -R "$username:$username" "$email_dir" 2>/dev/null
                chmod -R 660 "$email_dir" 2>/dev/null

                log_message "邮件数据还原成功"
                return 0
            fi
        done

        log_message "警告: 未找到邮件数据备份"
    else
        log_message "警告: 无法获取用户名或域名信息"
    fi
}

# 还原cron任务（适应云服务器环境）
restore_crontab() {
    log_message "开始还原cron任务..."

    if [ -f "$BACKUP_DIR/crontab.conf" ]; then
        local username=$(grep "^username=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)

        if [ -n "$username" ]; then
            log_message "发现cron配置文件，用户: $username"

            # 显示cron任务内容
            log_message "Cron任务内容:"
            while IFS= read -r line; do
                if [[ ! "$line" =~ ^[[:space:]]*$ ]] && [[ ! "$line" =~ ^PATH ]]; then
                    log_message "  $line"
                fi
            done < "$BACKUP_DIR/crontab.conf"

            # 尝试设置crontab（如果当前用户是目标用户或有sudo权限）
            if [ "$(whoami)" = "$username" ]; then
                crontab "$BACKUP_DIR/crontab.conf" 2>/dev/null && log_message "Cron任务还原成功" || log_message "警告: Cron任务还原失败"
            elif command -v sudo >/dev/null 2>&1; then
                sudo -u "$username" crontab "$BACKUP_DIR/crontab.conf" 2>/dev/null && log_message "Cron任务还原成功" || log_message "警告: Cron任务还原失败，请手动设置"
            else
                log_message "警告: 无法自动设置cron任务，请手动执行: crontab $BACKUP_DIR/crontab.conf"
            fi
        fi
    else
        log_message "警告: 未找到cron配置文件"
    fi
}

# 重启相关服务（适应云服务器环境）
restart_services() {
    log_message "检查并重启相关服务..."

    # 检查并重启Apache/Nginx
    if systemctl is-active --quiet httpd 2>/dev/null; then
        systemctl restart httpd 2>/dev/null && log_message "Apache重启成功" || log_message "Apache重启失败"
    elif systemctl is-active --quiet nginx 2>/dev/null; then
        systemctl restart nginx 2>/dev/null && log_message "Nginx重启成功" || log_message "Nginx重启失败"
    else
        log_message "未检测到Web服务器或无权限重启"
    fi

    # 检查并重启邮件服务
    if systemctl is-active --quiet dovecot 2>/dev/null; then
        systemctl restart dovecot 2>/dev/null && log_message "Dovecot重启成功" || log_message "Dovecot重启失败"
    fi

    if systemctl is-active --quiet exim 2>/dev/null; then
        systemctl restart exim 2>/dev/null && log_message "Exim重启成功" || log_message "Exim重启失败"
    fi

    # 检查并重启FTP服务
    if systemctl is-active --quiet proftpd 2>/dev/null; then
        systemctl restart proftpd 2>/dev/null && log_message "ProFTPD重启成功" || log_message "ProFTPD重启失败"
    fi

    log_message "服务检查完成"
}

# 主函数
main() {
    log_message "========== 开始自动还原备份 =========="
    
    # 设置陷阱以确保清理
    trap cleanup EXIT
    
    # 检查锁文件
    check_lock
    
    # 检查先决条件
    check_prerequisites
    
    # 备份当前状态
    backup_current_state
    
    # 执行还原步骤
    restore_user_config
    restore_databases
    restore_website_files
    restore_email_data
    restore_crontab
    
    # 重启服务
    restart_services
    
    log_message "========== 备份还原完成 =========="

    # 显示还原结果摘要
    if [ -f "$BACKUP_DIR/user.conf" ]; then
        local username=$(grep "^username=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
        local domain=$(grep "^domain=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)

        log_message "还原摘要:"
        log_message "  用户名: $username"
        log_message "  域名: $domain"
        log_message "  网站目录: /home/<USER>/domains/$domain/public_html"
        log_message "  邮件目录: /home/<USER>/imap/$domain"
        log_message "  日志文件: $LOG_FILE"
    fi

    # 清理
    cleanup
}

# 运行主函数
main "$@"
