#!/bin/bash

# 一键还原脚本
# 自动检查环境并运行最适合的还原脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo "=========================================="
echo "DirectAdmin 一键还原脚本"
echo "=========================================="

# 检查备份目录
if [ ! -d "$SCRIPT_DIR/backup" ]; then
    echo -e "${RED}错误: 备份目录不存在${NC}"
    echo "请确保备份文件位于 $SCRIPT_DIR/backup 目录中"
    exit 1
fi

echo -e "${GREEN}✓ 发现备份目录${NC}"

# 设置脚本权限
echo "设置脚本权限..."
chmod +x "$SCRIPT_DIR"/*.sh 2>/dev/null

# 运行备份检查
echo ""
echo "=========================================="
echo "第一步: 检查备份文件完整性"
echo "=========================================="

if [ -f "$SCRIPT_DIR/check_backup.sh" ]; then
    "$SCRIPT_DIR/check_backup.sh"
    
    echo ""
    echo -e "${YELLOW}是否继续进行还原? (y/n):${NC}"
    read -r response
    
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo "还原已取消"
        exit 0
    fi
else
    echo -e "${YELLOW}警告: 检查脚本不存在，直接进行还原${NC}"
fi

# 运行还原脚本
echo ""
echo "=========================================="
echo "第二步: 执行还原操作"
echo "=========================================="

# 选择最适合的还原脚本
if [ -f "$SCRIPT_DIR/restore_website.sh" ]; then
    echo "使用优化的还原脚本: restore_website.sh"
    "$SCRIPT_DIR/restore_website.sh"
elif [ -f "$SCRIPT_DIR/simple_restore.sh" ]; then
    echo "使用简化还原脚本: simple_restore.sh"
    "$SCRIPT_DIR/simple_restore.sh"
elif [ -f "$SCRIPT_DIR/cron.sh" ]; then
    echo "使用完整还原脚本: cron.sh"
    "$SCRIPT_DIR/cron.sh"
else
    echo -e "${RED}错误: 未找到可用的还原脚本${NC}"
    exit 1
fi

echo ""
echo "=========================================="
echo "还原完成！"
echo "=========================================="

# 读取用户信息用于显示结果
if [ -f "$SCRIPT_DIR/backup/user.conf" ]; then
    username=$(grep "^username=" "$SCRIPT_DIR/backup/user.conf" | cut -d'=' -f2)
    domain=$(grep "^domain=" "$SCRIPT_DIR/backup/user.conf" | cut -d'=' -f2)
    
    echo ""
    echo "请检查以下位置："
    echo "- 网站文件: /home/<USER>/domains/$domain/public_html"
    echo "- 邮件数据: /home/<USER>/imap/$domain"
    echo "- 日志文件: $SCRIPT_DIR/restore_*.log"
fi

echo ""
echo "如有问题，请查看日志文件获取详细信息。"
