# DirectAdmin 自动还原备份脚本

这是一个用于自动还原DirectAdmin备份文件的Shell脚本集合，专门针对云服务器环境优化。

## 文件说明

- `cron.sh` - 完整的还原脚本（适用于复杂环境）
- `restore_website.sh` - 简化的网站还原脚本（推荐使用）
- `check_backup.sh` - 备份文件完整性检查脚本
- `simple_restore.sh` - 基础还原脚本
- `setup.sh` - 快速设置脚本
- `restore_config.conf` - 配置文件
- `README.md` - 使用说明

## 快速开始

### 1. 检查备份文件
```bash
chmod +x check_backup.sh
./check_backup.sh
```

### 2. 运行还原（推荐）
```bash
chmod +x restore_website.sh
./restore_website.sh
```

## 功能特性

- ✅ 自动还原用户配置
- ✅ 还原数据库
- ✅ 还原网站文件
- ✅ 还原邮件数据
- ✅ 还原cron任务
- ✅ 自动重启相关服务
- ✅ 详细的日志记录
- ✅ 锁文件机制防止重复运行
- ✅ 还原前备份当前状态

## 安装和配置

### 1. 准备工作

确保您的系统满足以下要求：
- Linux系统（CentOS/RHEL/Ubuntu等）
- DirectAdmin控制面板
- Root权限
- 必要的工具：`zstd`, `tar`, `mysql`

### 2. 配置脚本

1. 编辑 `restore_config.conf` 文件：
```bash
nano restore_config.conf
```

2. 修改以下关键配置：
```bash
# 设置您的备份目录路径
BACKUP_DIR="/path/to/your/backup/directory"

# 根据需要调整其他配置项
```

3. 编辑 `cron.sh` 脚本，更新备份目录路径：
```bash
nano cron.sh
```

找到这一行并修改：
```bash
BACKUP_DIR="/path/to/backup"  # 修改为实际的备份目录路径
```

### 3. 设置权限

```bash
chmod +x cron.sh
chmod 600 restore_config.conf
```

## 使用方法

### 手动运行

```bash
# 以root用户运行
sudo ./cron.sh
```

### 设置定时任务

1. 编辑crontab：
```bash
crontab -e
```

2. 添加定时任务（例如每天凌晨2点运行）：
```bash
0 2 * * * /path/to/cron.sh >> /var/log/cron_restore.log 2>&1
```

### 一次性还原

如果您只需要运行一次还原，可以直接执行：
```bash
sudo ./cron.sh
```

## 备份文件结构

脚本支持以下DirectAdmin备份文件结构：

```
backup/
├── user.conf              # 用户配置
├── user.db                # 用户数据库信息
├── crontab.conf           # Cron任务
├── home.tar.zst           # 网站文件压缩包
├── email_data/            # 邮件数据
├── backup_options.list    # 备份选项
└── domain_name/           # 域名相关文件
    ├── domain.conf
    ├── database.sql
    └── ...
```

## 日志文件

- 默认日志位置：`/var/log/backup_restore.log`
- 日志包含详细的操作记录和错误信息
- 支持日志轮转以防止文件过大

## 安全注意事项

1. **备份当前状态**：脚本会在还原前自动备份当前系统状态
2. **权限检查**：脚本要求root权限运行
3. **锁文件机制**：防止多个还原进程同时运行
4. **配置文件权限**：建议设置为600权限保护敏感信息

## 故障排除

### 常见问题

1. **权限错误**
   - 确保以root用户运行脚本
   - 检查文件权限设置

2. **路径错误**
   - 验证备份目录路径是否正确
   - 确保所有必要的文件都存在

3. **服务启动失败**
   - 检查系统服务状态
   - 查看系统日志获取详细错误信息

4. **数据库还原失败**
   - 检查MySQL服务状态
   - 验证数据库权限

### 查看日志

```bash
# 查看最新日志
tail -f /var/log/backup_restore.log

# 查看错误信息
grep "错误" /var/log/backup_restore.log
```

## 自定义配置

您可以通过修改脚本中的变量来自定义行为：

- `BACKUP_DIR`：备份文件目录
- `LOG_FILE`：日志文件路径
- `DA_PATH`：DirectAdmin安装路径

## 支持的备份类型

- 用户账户配置
- 域名设置
- 数据库和数据
- 网站文件
- 邮件账户和数据
- FTP账户
- Cron任务
- SSL证书

## 注意事项

1. 还原过程会覆盖现有数据，请确保已备份重要信息
2. 建议在测试环境中先验证脚本功能
3. 大型备份文件的还原可能需要较长时间
4. 确保有足够的磁盘空间进行还原操作

## 版本信息

- 版本：1.0
- 兼容性：DirectAdmin 1.60+
- 测试环境：CentOS 7/8, Ubuntu 18.04/20.04

如有问题或建议，请查看日志文件或联系系统管理员。
