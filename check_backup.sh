#!/bin/bash

# 备份文件检查脚本
# 用于验证备份文件的完整性

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_DIR="$SCRIPT_DIR/backup"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo "=========================================="
echo "备份文件完整性检查"
echo "=========================================="

# 检查备份目录
if [ ! -d "$BACKUP_DIR" ]; then
    echo -e "${RED}✗ 备份目录不存在: $BACKUP_DIR${NC}"
    exit 1
fi

echo -e "${GREEN}✓ 备份目录存在: $BACKUP_DIR${NC}"
echo ""

# 检查关键文件
echo "检查关键文件:"
files_to_check=(
    "user.conf:用户配置文件"
    "user.db:用户数据库信息"
    "crontab.conf:Cron任务配置"
    "backup_options.list:备份选项列表"
    "home.tar.zst:网站文件压缩包"
)

for item in "${files_to_check[@]}"; do
    file=$(echo "$item" | cut -d':' -f1)
    desc=$(echo "$item" | cut -d':' -f2)
    
    if [ -f "$BACKUP_DIR/$file" ]; then
        size=$(ls -lh "$BACKUP_DIR/$file" | awk '{print $5}')
        echo -e "${GREEN}✓ $desc ($file) - 大小: $size${NC}"
    else
        echo -e "${YELLOW}⚠ $desc ($file) - 未找到${NC}"
    fi
done

echo ""

# 读取并显示用户信息
if [ -f "$BACKUP_DIR/user.conf" ]; then
    echo "用户配置信息:"
    echo "----------------------------------------"
    
    # 提取关键信息
    username=$(grep "^username=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
    domain=$(grep "^domain=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
    email=$(grep "^email=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
    quota=$(grep "^quota=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
    package=$(grep "^package=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
    date_created=$(grep "^date_created=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
    
    echo -e "${BLUE}用户名:${NC} $username"
    echo -e "${BLUE}主域名:${NC} $domain"
    echo -e "${BLUE}邮箱:${NC} $email"
    echo -e "${BLUE}配额:${NC} ${quota}MB"
    echo -e "${BLUE}套餐:${NC} $package"
    echo -e "${BLUE}创建时间:${NC} $date_created"
    echo ""
fi

# 检查域名相关文件
if [ -n "$domain" ] && [ -d "$BACKUP_DIR/$domain" ]; then
    echo "域名相关文件:"
    echo "----------------------------------------"
    
    domain_files=(
        "domain.conf:域名配置"
        "domain.ip_list:IP列表"
        "subdomain.list:子域名列表"
        "ftp.conf:FTP配置"
        "ftp.passwd:FTP密码"
        "${domain}.db:域名数据库"
    )
    
    for item in "${domain_files[@]}"; do
        file=$(echo "$item" | cut -d':' -f1)
        desc=$(echo "$item" | cut -d':' -f2)
        
        if [ -f "$BACKUP_DIR/$domain/$file" ]; then
            size=$(ls -lh "$BACKUP_DIR/$domain/$file" | awk '{print $5}')
            echo -e "${GREEN}✓ $desc ($file) - 大小: $size${NC}"
        else
            echo -e "${YELLOW}⚠ $desc ($file) - 未找到${NC}"
        fi
    done
    echo ""
fi

# 检查邮件数据
echo "邮件数据检查:"
echo "----------------------------------------"
email_dirs=(
    "email_data:邮件数据目录"
    "$domain/email:域名邮件目录"
)

for item in "${email_dirs[@]}"; do
    dir=$(echo "$item" | cut -d':' -f1)
    desc=$(echo "$item" | cut -d':' -f2)
    
    if [ -d "$BACKUP_DIR/$dir" ]; then
        count=$(find "$BACKUP_DIR/$dir" -type f 2>/dev/null | wc -l)
        echo -e "${GREEN}✓ $desc - 文件数量: $count${NC}"
    else
        echo -e "${YELLOW}⚠ $desc - 未找到${NC}"
    fi
done

# 检查其他目录
echo ""
echo "其他目录检查:"
echo "----------------------------------------"
if [ -d "$SCRIPT_DIR/domains" ]; then
    echo -e "${GREEN}✓ domains 目录存在${NC}"
fi

if [ -d "$SCRIPT_DIR/imap" ]; then
    echo -e "${GREEN}✓ imap 目录存在${NC}"
fi

# 检查压缩文件
echo ""
echo "压缩文件检查:"
echo "----------------------------------------"
if [ -f "$BACKUP_DIR/home.tar.zst" ]; then
    size=$(ls -lh "$BACKUP_DIR/home.tar.zst" | awk '{print $5}')
    echo -e "${GREEN}✓ home.tar.zst 存在 - 大小: $size${NC}"
    
    # 尝试测试压缩文件完整性
    if command -v zstd >/dev/null 2>&1; then
        if zstd -t "$BACKUP_DIR/home.tar.zst" 2>/dev/null; then
            echo -e "${GREEN}✓ 压缩文件完整性验证通过${NC}"
        else
            echo -e "${RED}✗ 压缩文件可能损坏${NC}"
        fi
    else
        echo -e "${YELLOW}⚠ zstd 未安装，无法验证压缩文件完整性${NC}"
    fi
else
    echo -e "${YELLOW}⚠ home.tar.zst 未找到${NC}"
fi

# 检查cron配置
echo ""
echo "Cron任务检查:"
echo "----------------------------------------"
if [ -f "$BACKUP_DIR/crontab.conf" ]; then
    echo -e "${GREEN}✓ crontab.conf 存在${NC}"
    echo "Cron任务内容:"
    while IFS= read -r line; do
        if [[ ! "$line" =~ ^[[:space:]]*$ ]] && [[ ! "$line" =~ ^PATH ]]; then
            echo -e "${BLUE}  $line${NC}"
        fi
    done < "$BACKUP_DIR/crontab.conf"
else
    echo -e "${YELLOW}⚠ crontab.conf 未找到${NC}"
fi

echo ""
echo "=========================================="
echo "检查完成！"
echo "=========================================="

# 提供建议
echo ""
echo "建议的还原步骤:"
echo "1. 运行还原脚本: ./restore_website.sh"
echo "2. 检查网站文件: /home/<USER>/domains/$domain/public_html"
echo "3. 验证邮件数据: /home/<USER>/imap/$domain"
echo "4. 测试网站功能"
echo ""

if [ ! -f "restore_website.sh" ]; then
    echo -e "${YELLOW}注意: 还原脚本 restore_website.sh 不存在${NC}"
fi
