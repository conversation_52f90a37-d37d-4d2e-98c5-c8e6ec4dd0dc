#!/bin/bash

# 云服务器网站还原脚本
# 专门针对您的环境优化

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BACKUP_DIR="$SCRIPT_DIR/backup"
LOG_FILE="$SCRIPT_DIR/restore_$(date +%Y%m%d_%H%M%S).log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        "INFO")  echo -e "${BLUE}[INFO]${NC} [$timestamp] $message" | tee -a "$LOG_FILE" ;;
        "SUCCESS") echo -e "${GREEN}[SUCCESS]${NC} [$timestamp] $message" | tee -a "$LOG_FILE" ;;
        "WARNING") echo -e "${YELLOW}[WARNING]${NC} [$timestamp] $message" | tee -a "$LOG_FILE" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} [$timestamp] $message" | tee -a "$LOG_FILE" ;;
    esac
}

# 读取备份配置
read_backup_config() {
    log "INFO" "读取备份配置..."
    
    if [ ! -f "$BACKUP_DIR/user.conf" ]; then
        log "ERROR" "找不到用户配置文件: $BACKUP_DIR/user.conf"
        exit 1
    fi
    
    # 读取配置信息
    USERNAME=$(grep "^username=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
    DOMAIN=$(grep "^domain=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
    EMAIL=$(grep "^email=" "$BACKUP_DIR/user.conf" | cut -d'=' -f2)
    USER_HOME="/home/<USER>"
    
    log "INFO" "配置信息:"
    log "INFO" "  用户名: $USERNAME"
    log "INFO" "  主域名: $DOMAIN"
    log "INFO" "  邮箱: $EMAIL"
    log "INFO" "  用户目录: $USER_HOME"
}

# 检查并创建目录结构
setup_directories() {
    log "INFO" "设置目录结构..."
    
    # 创建必要的目录
    local dirs=(
        "$USER_HOME"
        "$USER_HOME/domains"
        "$USER_HOME/domains/$DOMAIN"
        "$USER_HOME/domains/$DOMAIN/public_html"
        "$USER_HOME/domains/$DOMAIN/private_html"
        "$USER_HOME/imap"
        "$USER_HOME/imap/$DOMAIN"
        "$USER_HOME/.ssh"
    )
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log "INFO" "创建目录: $dir"
        fi
    done
    
    log "SUCCESS" "目录结构设置完成"
}

# 还原网站文件
restore_website_files() {
    log "INFO" "开始还原网站文件..."
    
    if [ -f "$BACKUP_DIR/home.tar.zst" ]; then
        log "INFO" "发现压缩备份文件: home.tar.zst"
        
        # 创建临时目录
        local temp_dir="/tmp/restore_$$"
        mkdir -p "$temp_dir"
        
        # 解压文件
        log "INFO" "正在解压备份文件..."
        cd "$temp_dir"
        
        if command -v zstd >/dev/null 2>&1; then
            zstd -d -c "$BACKUP_DIR/home.tar.zst" | tar -xf - 2>/dev/null
        else
            log "ERROR" "zstd 命令未找到，尝试其他解压方法..."
            # 尝试其他解压方法
            tar -I zstd -xf "$BACKUP_DIR/home.tar.zst" 2>/dev/null || {
                log "ERROR" "无法解压文件，请确保安装了 zstd"
                rm -rf "$temp_dir"
                return 1
            }
        fi
        
        # 检查解压结果
        if [ -d "$temp_dir/home/<USER>" ]; then
            log "SUCCESS" "文件解压成功"
            
            # 备份现有文件（如果存在）
            if [ -d "$USER_HOME" ] && [ "$(ls -A $USER_HOME 2>/dev/null)" ]; then
                local backup_name="${USER_HOME}.backup.$(date +%s)"
                mv "$USER_HOME" "$backup_name"
                log "INFO" "现有文件已备份到: $backup_name"
            fi
            
            # 移动解压的文件
            mv "$temp_dir/home/<USER>" "$USER_HOME"
            log "SUCCESS" "网站文件还原完成"
            
            # 设置基本权限
            chmod 755 "$USER_HOME"
            if [ -d "$USER_HOME/domains/$DOMAIN/public_html" ]; then
                chmod 755 "$USER_HOME/domains/$DOMAIN/public_html"
            fi
            
        else
            log "ERROR" "解压后未找到用户目录"
        fi
        
        # 清理临时目录
        rm -rf "$temp_dir"
        
    else
        log "WARNING" "未找到网站文件备份 (home.tar.zst)"
    fi
}

# 还原域名配置
restore_domain_config() {
    log "INFO" "还原域名配置..."
    
    local domain_backup="$BACKUP_DIR/$DOMAIN"
    if [ -d "$domain_backup" ]; then
        log "INFO" "发现域名配置目录: $domain_backup"
        
        # 复制域名配置文件到用户目录
        local target_dir="$USER_HOME/domains/$DOMAIN"
        
        # 复制各种配置文件
        for config_file in domain.conf domain.ip_list subdomain.list ftp.conf ftp.passwd; do
            if [ -f "$domain_backup/$config_file" ]; then
                cp "$domain_backup/$config_file" "$target_dir/"
                log "INFO" "已复制: $config_file"
            fi
        done
        
        log "SUCCESS" "域名配置还原完成"
    else
        log "WARNING" "未找到域名配置目录"
    fi
}

# 还原数据库
restore_database() {
    log "INFO" "检查数据库备份..."
    
    # 查找数据库文件
    local db_files=(
        "$BACKUP_DIR/$DOMAIN/${DOMAIN}.db"
        "$BACKUP_DIR/${DOMAIN}.db"
        "$BACKUP_DIR/user.db"
    )
    
    for db_file in "${db_files[@]}"; do
        if [ -f "$db_file" ]; then
            log "INFO" "发现数据库备份: $db_file"
            
            # 尝试还原数据库
            local db_name="${USERNAME}_$(echo $DOMAIN | tr '.' '_')"
            
            if command -v mysql >/dev/null 2>&1; then
                log "INFO" "正在还原数据库: $db_name"
                
                # 创建数据库
                mysql -e "CREATE DATABASE IF NOT EXISTS \`$db_name\`;" 2>/dev/null
                
                # 导入数据
                if mysql "$db_name" < "$db_file" 2>/dev/null; then
                    log "SUCCESS" "数据库还原成功: $db_name"
                else
                    log "WARNING" "数据库还原可能失败，请检查"
                fi
            else
                log "WARNING" "MySQL 客户端未找到，跳过数据库还原"
            fi
            break
        fi
    done
}

# 还原邮件数据
restore_email() {
    log "INFO" "还原邮件数据..."
    
    local email_sources=(
        "$BACKUP_DIR/email_data"
        "$BACKUP_DIR/$DOMAIN/email"
        "$SCRIPT_DIR/imap/$DOMAIN"
    )
    
    for email_source in "${email_sources[@]}"; do
        if [ -d "$email_source" ]; then
            log "INFO" "发现邮件数据: $email_source"
            
            local target_dir="$USER_HOME/imap/$DOMAIN"
            mkdir -p "$target_dir"
            
            # 复制邮件数据
            cp -r "$email_source"/* "$target_dir/" 2>/dev/null
            
            # 设置权限
            chmod -R 600 "$target_dir" 2>/dev/null
            
            log "SUCCESS" "邮件数据还原完成"
            break
        fi
    done
}

# 还原cron任务
restore_crontab() {
    log "INFO" "还原cron任务..."
    
    if [ -f "$BACKUP_DIR/crontab.conf" ]; then
        log "INFO" "发现cron配置文件"
        
        # 显示cron内容
        log "INFO" "Cron任务内容:"
        while IFS= read -r line; do
            if [[ ! "$line" =~ ^[[:space:]]*$ ]] && [[ ! "$line" =~ ^# ]]; then
                log "INFO" "  $line"
            fi
        done < "$BACKUP_DIR/crontab.conf"
        
        # 尝试设置crontab
        if crontab "$BACKUP_DIR/crontab.conf" 2>/dev/null; then
            log "SUCCESS" "Cron任务还原成功"
        else
            log "WARNING" "Cron任务还原失败，请手动设置"
        fi
    else
        log "WARNING" "未找到cron配置文件"
    fi
}

# 显示还原摘要
show_summary() {
    log "INFO" "========== 还原摘要 =========="
    log "INFO" "用户名: $USERNAME"
    log "INFO" "域名: $DOMAIN"
    log "INFO" "邮箱: $EMAIL"
    log "INFO" "用户目录: $USER_HOME"
    log "INFO" "备份目录: $BACKUP_DIR"
    log "INFO" "日志文件: $LOG_FILE"
    
    # 检查关键目录
    if [ -d "$USER_HOME/domains/$DOMAIN/public_html" ]; then
        log "SUCCESS" "网站目录: $USER_HOME/domains/$DOMAIN/public_html"
    fi
    
    if [ -d "$USER_HOME/imap/$DOMAIN" ]; then
        log "SUCCESS" "邮件目录: $USER_HOME/imap/$DOMAIN"
    fi
    
    log "INFO" "================================"
}

# 主函数
main() {
    echo "=========================================="
    echo "云服务器网站还原脚本"
    echo "=========================================="
    
    log "INFO" "开始还原流程..."
    
    # 检查备份目录
    if [ ! -d "$BACKUP_DIR" ]; then
        log "ERROR" "备份目录不存在: $BACKUP_DIR"
        exit 1
    fi
    
    # 执行还原步骤
    read_backup_config
    setup_directories
    restore_website_files
    restore_domain_config
    restore_database
    restore_email
    restore_crontab
    
    # 显示摘要
    show_summary
    
    log "SUCCESS" "还原流程完成！"
    echo ""
    echo "请检查以下目录："
    echo "- 网站文件: $USER_HOME/domains/$DOMAIN/public_html"
    echo "- 邮件数据: $USER_HOME/imap/$DOMAIN"
    echo "- 日志文件: $LOG_FILE"
}

# 运行主函数
main "$@"
